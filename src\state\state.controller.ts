import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Request,
  HttpStatus,
  HttpCode,
  NotFoundException,
} from '@nestjs/common';
import { Request as ExpressRequest } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { StateService } from './state.service';
import { State } from './entity/state.entity';
import { StateResponseDto } from './dto/state-response.dto';
import { CreateStateDto } from './dto/create-state.dto';
import { UpdateStateDto } from './dto/update-state.dto';
import { AddRegionDto } from './dto/add-region.dto';
import { DeleteResult } from 'typeorm';

@ApiTags('States')
@Controller('states')
export class StateController {
  constructor(private readonly stateService: StateService) {}

  @ApiOperation({ summary: 'Get all states' })
  @ApiResponse({
    status: 200,
    description: 'List of all states',
    type: [State],
  })
  @Get()
  async findAll(): Promise<State[]> {
    return this.stateService.findAll();
  }

  @ApiOperation({ summary: "Get current user's state" })
  @ApiResponse({
    status: 200,
    description: "Current user's state with region population",
    type: StateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User does not have a state',
  })
  @Get('my-state')
  async getMyState(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<State> {
    return await this.stateService.findByUserId(req.user.userId);
  }

  @ApiOperation({ summary: "Get current user's state if he is leader" })
  @ApiResponse({
    status: 200,
    description: "Current user's state with region population",
    type: StateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User does not have a state',
  })
  @Get('leader')
  async getStateLeader(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<State | null> {
    return await this.stateService.findStateLeader(req.user.userId);
  }

  @ApiOperation({ summary: 'Get state by ID' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiResponse({
    status: 200,
    description: 'State details with region population',
    type: StateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Get(':id')
  async findById(@Param('id') id: string): Promise<State> {
    return this.stateService.findById(id);
  }

  @ApiOperation({ summary: 'Get state resources' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiResponse({
    status: 200,
    description: 'State resources information',
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Get(':id/resources')
  async getStateResources(@Param('id') id: string): Promise<any> {
    return this.stateService.getStateResources(id);
  }

  @ApiOperation({ summary: 'Create a new state' })
  @ApiResponse({
    status: 201,
    description: 'State has been created',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid state creation parameters',
  })
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Request() req: ExpressRequest & { user: { userId: number } },
    @Body() createStateDto: CreateStateDto,
  ): Promise<State> {
    return this.stateService.create(req.user.userId, createStateDto);
  }

  @ApiOperation({ summary: 'Update a state' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiResponse({
    status: 200,
    description: 'State has been updated',
    type: State,
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateStateDto: UpdateStateDto,
  ): Promise<State> {
    return this.stateService.update(id, updateStateDto);
  }

  @ApiOperation({ summary: 'Add a region to a state' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiResponse({
    status: 200,
    description: 'Region has been added to the state',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'Region already belongs to another state',
  })
  @ApiResponse({
    status: 404,
    description: 'State or region not found',
  })
  @Post(':id/regions')
  async addRegion(
    @Param('id') id: string,
    @Body() addRegionDto: AddRegionDto,
  ): Promise<State> {
    return this.stateService.addRegion(id, addRegionDto);
  }

  @ApiOperation({ summary: 'Remove a region from a state' })
  @ApiParam({ name: 'stateId', description: 'State ID' })
  @ApiParam({ name: 'regionId', description: 'Region ID' })
  @ApiResponse({
    status: 200,
    description: 'Region has been removed from the state',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'Region does not belong to this state',
  })
  @ApiResponse({
    status: 404,
    description: 'State or region not found',
  })
  @Delete(':stateId/regions/:regionId')
  async removeRegion(
    @Param('stateId') stateId: string,
    @Param('regionId') regionId: string,
  ): Promise<State> {
    return this.stateService.removeRegion(stateId, regionId);
  }

  @ApiOperation({ summary: 'Remove a state' })
  @ApiParam({ name: 'stateId', description: 'State ID' })
  @ApiResponse({
    status: 200,
    description: 'State has been removed',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'State is not empty',
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Delete(':stateId')
  async removeState(
    @Param('stateId') stateId: string,
  ): Promise<DeleteResult> {
    return this.stateService.removeState(stateId);
  }

  @ApiOperation({ summary: 'Change state leader' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiParam({ name: 'userId', description: 'New leader user ID' })
  @ApiResponse({
    status: 200,
    description: 'State leader has been changed',
    type: State,
  })
  @ApiResponse({
    status: 404,
    description: 'State or user not found',
  })
  @Post(':id/change-leader/:userId')
  async changeLeader(
    @Param('id') id: string,
    @Param('userId') userId: number,
  ): Promise<State> {
    return this.stateService.changeLeader(id, userId);
  }

  @ApiOperation({ summary: 'Add an ally to a state' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiParam({ name: 'allyId', description: 'Ally state ID' })
  @ApiResponse({
    status: 200,
    description: 'Ally has been added',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'States are already allies',
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Post(':id/allies/:allyId')
  async addAlly(
    @Param('id') id: string,
    @Param('allyId') allyId: string,
  ): Promise<State> {
    return this.stateService.addAlly(id, allyId);
  }

  @ApiOperation({ summary: 'Add an enemy to a state' })
  @ApiParam({ name: 'id', description: 'State ID' })
  @ApiParam({ name: 'enemyId', description: 'Enemy state ID' })
  @ApiResponse({
    status: 200,
    description: 'Enemy has been added',
    type: State,
  })
  @ApiResponse({
    status: 400,
    description: 'States are already enemies',
  })
  @ApiResponse({
    status: 404,
    description: 'State not found',
  })
  @Post(':id/enemies/:enemyId')
  async addEnemy(
    @Param('id') id: string,
    @Param('enemyId') enemyId: string,
  ): Promise<State> {
    return this.stateService.addEnemy(id, enemyId);
  }
}
